# Data-Grid 代码风格和约定

## 文件命名约定

### 组件文件
- Vue 组件：`PascalCase.vue` (如: `DataGrid.vue`, `DGToolbarAction.vue`)
- 组合式函数：`camelCase.ts` (如: `useDataGrid.ts`)
- 工具函数：`camelCase.ts` (如: `dataHelpers.ts`)

### 目录命名
- 模块目录：`kebab-case` (如: `data-grid`)
- 功能目录：`camelCase` (如: `composables`, `renderers`)

## 代码风格

### TypeScript 约定
- 优先使用接口定义 (`interface`) 而非类型别名
- 使用泛型确保类型安全
- 导出类型时使用 `export type` 或 `export interface`

### Vue 组件约定
- 使用 `<script setup lang="ts">` 语法
- Props 定义使用 `defineProps<T>()`
- 事件定义使用 `defineEmits<T>()`

### 样式约定
- 优先使用 Tailwind CSS 类名
- 避免内联样式，使用样式工具函数
- 组件样式使用 `scoped` 或组合式API

## 命名约定

### 变量和函数
- 使用 `camelCase`
- 布尔值以 `is`, `has`, `can` 开头
- 事件处理函数以 `handle` 开头

### 常量
- 使用 `UPPER_SNAKE_CASE`
- 在 `constants/` 目录集中管理

### 接口和类型
- 接口使用 `PascalCase`
- 类型别名使用 `PascalCase`
- 泛型参数使用单个大写字母 (`T`, `K`, `V`)

## 注释和文档

### JSDoc 注释
```typescript
/**
 * 创建数据网格实例
 * @param moduleModel 模块模型标识
 * @param gridOptions 网格配置选项
 * @returns 数据网格实例
 */
```

### Vue 组件注释
```vue
<!-- 数据网格主组件 -->
<template>
  <!-- 工具栏区域 -->
  <DGToolbar />
  <!-- 表格区域 -->
  <vxe-grid />
</template>
```

## 错误处理

### 错误处理模式
- 使用统一的错误处理器 (`utils/ErrorHandler.ts`)
- 提供用户友好的错误信息
- 在开发环境保留详细错误信息