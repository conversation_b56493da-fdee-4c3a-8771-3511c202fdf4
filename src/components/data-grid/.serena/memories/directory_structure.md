# Data-Grid 目录结构

## 核心模块组织

```
src/components/data-grid/
├── index.ts                 # 主入口文件
├── types/                   # 类型定义
│   └── index.ts            # 所有类型定义的集中导出
├── core/                    # 核心组件
│   ├── DataGrid.vue        # 主数据网格组件
│   ├── DataGridLoading.vue # 加载状态组件
│   ├── DGToolbar.vue       # 工具栏主组件
│   ├── DGToolbarAction.vue # 工具栏操作组件
│   ├── DGToolbarButton.vue # 工具栏按钮组件
│   ├── DGToolbarRange.vue  # 分页范围组件
│   ├── DGToolbarTitle.vue  # 工具栏标题组件
│   └── DGToolbarSearch/    # 搜索功能模块
├── composables/             # 组合式函数
│   └── useDataGrid.ts      # 主要的数据网格逻辑
├── plugins/                 # 插件系统
│   ├── index.ts            # 插件工厂和管理
│   ├── core/               # 插件核心
│   ├── renderers/          # 渲染器插件
│   └── presets/            # 预设配置
├── renderers/               # Vue 组件渲染器
│   ├── LinkRenderer.vue
│   ├── StatusRenderer.vue
│   ├── CompositeRenderer.vue
│   ├── ActionsRenderer.vue
│   └── RatingRenderer.vue
├── utils/                   # 工具函数
├── constants/               # 常量定义
├── docs/                    # 文档
└── __tests__/               # 测试文件
```

## 关键文件说明

### 入口和配置
- `index.ts`: 主要导出接口
- `types/index.ts`: 集中的类型定义

### 核心组件
- `core/DataGrid.vue`: 主组件，处理插槽渲染和数据绑定
- `composables/useDataGrid.ts`: 核心业务逻辑

### 插件架构
- `plugins/`: 完整的插件系统实现
- `renderers/`: Vue组件形式的渲染器

### 工具和辅助
- `utils/`: 各种辅助函数和帮助器
- `constants/`: 常量和默认配置