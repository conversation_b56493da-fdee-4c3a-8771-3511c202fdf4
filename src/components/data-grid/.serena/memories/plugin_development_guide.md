# Data-Grid 现代插件开发指南

## 现代插件系统概述

Data-Grid 的现代插件系统基于**依赖注入容器**和**服务架构**设计，提供了企业级的插件开发体验。系统支持多种插件类型，具备完整的生命周期管理、配置验证、性能监控等特性。

## 系统架构

### 核心组件
- **ModernPluginManager**: 现代插件管理器
- **InjectionContainer**: 依赖注入容器
- **PluginFactory**: 插件工厂
- **ConfigStore**: 安全配置存储
- **DevUtils**: 开发工具集

### 插件类型分类

#### 1. 渲染器插件 (Renderer Plugins)
专门用于数据单元格的渲染显示

#### 2. 服务插件 (Service Plugins)  
提供特定功能的服务组件

#### 3. 扩展点插件 (Extension Plugins)
扩展现有功能的插件

#### 4. 复合插件 (Composite Plugins)
包含多种功能的综合插件

## 开发环境设置

### 1. 开发工具初始化
```typescript
import { DevUtils } from '@/components/data-grid/plugins/types/dev-utils'

// 创建开发环境
const devEnv = DevUtils.createTestEnvironment()

// 模拟服务
DevUtils.mockService('config', mockConfigService)
```

### 2. 插件工厂使用
```typescript
import { PluginFactory } from '@/components/data-grid/plugins/core/PluginFactory'

const factory = PluginFactory.getInstance()

// 创建现代插件管理器
const manager = factory.createModernManager({
  debug: true,
  preset: 'development'
})
```

## 现代渲染器开发

### 1. 基础渲染器结构

```typescript
import { defineRenderer, InferConfigType } from '@/components/data-grid/plugins'

// 定义配置 Schema
const MyRendererConfigSchema = {
  displayText: { type: 'string', default: '', description: '显示文本' },
  showIcon: { type: 'boolean', default: true, description: '是否显示图标' },
  variant: { 
    type: 'string', 
    enum: ['default', 'primary', 'success'], 
    default: 'default',
    description: '样式变体'
  }
} as const

type MyRendererConfig = InferConfigType<typeof MyRendererConfigSchema>

// 定义渲染器
export const MyRendererDefinition = defineRenderer({
  name: 'my-renderer',
  component: 'MyRendererComponent', // Vue 组件名
  defaultConfig: {
    displayText: '默认文本',
    showIcon: true,
    variant: 'default'
  },
  defaultWidth: 120,
  configSchema: MyRendererConfigSchema,
  validator: (config: MyRendererConfig) => {
    if (!config.displayText && config.showIcon === false) {
      return { valid: false, errors: ['必须提供显示文本或图标'] }
    }
    return { valid: true }
  }
})
```

### 2. Vue 渲染器组件

```vue
<template>
  <div :class="wrapperClasses">
    <Icon v-if="config.showIcon" :name="iconName" />
    <span v-if="displayText" :class="textClasses">{{ displayText }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRendererConfig } from '@/components/data-grid/plugins'
import type { RendererParams } from '@/components/data-grid/plugins/types'

interface Props {
  params: RendererParams
}

const props = defineProps<Props>()

// 使用渲染器配置
const config = useRendererConfig<MyRendererConfig>()

// 计算属性
const displayText = computed(() => {
  return config.displayText || String(props.params.value)
})

const wrapperClasses = computed(() => {
  return [
    'my-renderer',
    `my-renderer--${config.variant}`,
    {
      'my-renderer--with-icon': config.showIcon
    }
  ]
})

const iconName = computed(() => {
  // 根据配置和值确定图标
  return config.variant === 'success' ? 'check' : 'info'
})
</script>
```

### 3. 插件注册和配置

```typescript
import { createModernPluginManager } from '@/components/data-grid/plugins'

// 创建插件定义
export const MyPlugin: PluginDefinition = {
  name: 'my-plugin',
  version: '1.0.0',
  description: '我的自定义插件',
  
  // 提供的服务
  provides: [
    {
      token: 'MyRendererService',
      provider: {
        factory: () => new MyRendererService(),
        singleton: true
      }
    }
  ],
  
  // 依赖的服务
  requires: ['ConfigService', 'ThemeService'],
  
  // 列助手方法注册
  columnHelperMethods: [
    {
      name: 'myRenderer',
      implementation: (field: string, title: string, config: MyRendererConfig = {}) => {
        return {
          field,
          title,
          plugin: 'my-renderer',
          pluginConfig: config,
          width: config.width || 120
        }
      },
      rendererConfig: {
        component: 'MyRendererComponent',
        defaultWidth: 120
      },
      description: '创建自定义渲染器列'
    }
  ],
  
  // 插件初始化
  setup: async (context: PluginContext) => {
    const { container, app } = context
    
    // 注册 Vue 组件
    if (app) {
      app.component('MyRendererComponent', MyRendererComponent)
    }
    
    // 注册渲染器
    const manager = container.resolveService<ModernPluginManager>('PluginManager')
    if (manager) {
      const helper = manager.getColumnHelper()
      helper.registerRenderer('my-renderer', {
        component: 'MyRendererComponent',
        defaultWidth: 120
      })
    }
    
    context.utils.logger.info('MyPlugin 初始化完成')
  },
  
  // 插件卸载
  teardown: async (context: PluginContext) => {
    context.utils.logger.info('MyPlugin 卸载完成')
  }
}

// 注册插件
const manager = createModernPluginManager()
await manager.registerPlugin(MyPlugin)
```

## 高级功能开发

### 1. 服务开发

```typescript
import { defineService } from '@/components/data-grid/plugins'

// 定义服务接口
interface MyService {
  process(data: any): Promise<any>
  validate(input: any): boolean
}

// 实现服务
class MyServiceImpl implements MyService {
  async process(data: any): Promise<any> {
    // 业务逻辑处理
    return processedData
  }
  
  validate(input: any): boolean {
    // 验证逻辑
    return true
  }
}

// 注册服务
export const MyServiceDefinition = defineService(
  'MyService', // 服务标识
  {
    factory: () => new MyServiceImpl(),
    singleton: true,
    dependencies: ['ConfigService'] // 依赖的其他服务
  }
)
```

### 2. 扩展点开发

```typescript
import { defineExtension } from '@/components/data-grid/plugins'

// 定义扩展点
export const MyExtension = defineExtension(
  'data-transform', // 扩展点名称
  (context: ExtensionContext, data: any) => {
    // 扩展逻辑
    return transformedData
  },
  {
    priority: 10, // 优先级
    description: '数据转换扩展'
  }
)
```

### 3. 配置验证器

```typescript
import { ConfigValidator } from '@/components/data-grid/plugins/types/dev-utils'

const validator = new ConfigValidator<MyRendererConfig>()
  .required('displayText', '显示文本是必需的')
  .string('variant', {
    pattern: /^(default|primary|success)$/,
    maxLength: 20
  })
  .number('width', {
    min: 50,
    max: 500,
    integer: true
  })

// 验证配置
const result = validator.validate(config)
if (!result.valid) {
  console.error('配置验证失败:', result.errors)
}
```

## 开发工具和调试

### 1. 性能监控

```typescript
import { monitor, handleErrors } from '@/components/data-grid/plugins/types/dev-utils'

class MyRenderer {
  @monitor('render-operation')
  @handleErrors({ category: 'RENDER', fallback: '' })
  render(params: RendererParams): string {
    // 渲染逻辑
    return result
  }
}
```

### 2. 开发时验证

```typescript
import { validateParams } from '@/components/data-grid/plugins/types/dev-utils'

class MyService {
  @validateParams({
    0: (value) => typeof value === 'string' || '参数必须是字符串',
    1: (value) => value > 0 || '参数必须大于0'
  })
  process(text: string, count: number) {
    // 处理逻辑
  }
}
```

### 3. 插件模板生成

```typescript
import { PluginDevHelper } from '@/components/data-grid/plugins/core/DevUtils'

// 生成插件模板
const template = PluginDevHelper.createModernPluginTemplate('MyPlugin', {
  type: 'renderer',
  withPerformanceMonitoring: true,
  withConfigValidation: true,
  withErrorHandling: true
})

console.log(template) // 完整的插件代码模板
```

## 最佳实践

### 1. 类型安全
- 总是使用 TypeScript 定义插件
- 利用 `InferConfigType` 自动推导配置类型
- 为所有配置项提供 Schema 验证

### 2. 性能优化
- 使用 `@monitor` 装饰器监控性能
- 合理使用缓存机制
- 避免在渲染函数中进行昂贵计算

### 3. 错误处理
- 使用 `@handleErrors` 装饰器处理异常
- 提供合理的降级策略
- 记录详细的错误上下文

### 4. 配置设计
- 提供合理的默认值
- 支持运行时配置验证
- 使用环境相关的配置

### 5. 文档和测试
- 为每个插件提供完整文档
- 编写单元测试和集成测试
- 提供使用示例和最佳实践

## 插件发布和分发

### 1. 插件打包
```typescript
// 插件入口文件
export { MyPlugin } from './MyPlugin'
export { MyRendererDefinition } from './renderers/MyRenderer'
export type { MyRendererConfig } from './types'
```

### 2. 版本管理
- 遵循语义化版本规范
- 提供版本兼容性检查
- 维护变更日志

### 3. 插件市场
- 注册到插件注册表
- 提供完整的插件描述
- 包含使用说明和示例