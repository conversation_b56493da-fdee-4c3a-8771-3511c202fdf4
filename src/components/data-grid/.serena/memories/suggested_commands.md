# Data-Grid 开发建议的命令

由于这是一个组件库子模块，没有独立的 package.json，因此开发命令需要在父项目中执行。

## 开发命令

### 类型检查

```bash
# 在父项目根目录执行
pnpm run type:check
```

### 代码风格检查

```bash
# 在父项目根目录执行
pnpm run lint
pnpm run lint:fix  # 自动修复
```

### 测试

```bash
# 运行测试
pnpm run test
# 运行特定测试
pnpm run test -- __tests__/unit/plugin-system.test.ts
```

### 开发服务器

```bash
# 启动开发服务器
pnpm run dev
```

### 构建

```bash
# 构建项目
pnpm run build
```

## 组件开发流程

1. **开发新功能**:

   - 在相应模块目录创建文件
   - 更新类型定义 (`types/index.ts`)
   - 添加到主入口 (`index.ts`)

2. **测试功能**:

   - 在 `__tests__/` 目录添加测试
   - 使用示例文件验证功能

3. **文档更新**:
   - 更新 `docs/` 目录下的相关文档
   - 确保API文档同步

## 系统命令 (macOS)

### 文件操作

```bash
ls -la                    # 列出文件
find . -name "*.vue"      # 查找Vue文件
grep -r "pattern" .       # 搜索内容
```

### Git 操作

```bash
git status               # 查看状态
git add .               # 添加更改
git commit -m "message" # 提交更改
git push                # 推送更改
```
