<template>
  <!-- 
    CompositeRenderer 的 variant 处理逻辑已与 ActionsRenderer 保持一致：
    1. 支持 type 属性进行自动映射
    2. 支持直接设置 variant 属性
    3. 默认 variant 为 'outline'
    4. 下拉菜单项也支持 variant 效果（颜色和悬停状态）
  -->
  <TooltipProvider>
    <div
      ref="containerRef"
      :class="containerClasses"
      @mouseenter="isHovered = true"
      @mouseleave="handleMouseLeave"
    >
      <!-- 图标区域 -->
      <div v-if="iconConfig" class="flex-shrink-0">
        <!-- 头像 -->
        <Avatar
          v-if="iconConfig.type === 'avatar'"
          :class="iconConfig.sizeClass"
        >
          <AvatarImage
            v-if="avatarInfo?.image"
            :src="avatarInfo.image"
            :alt="avatarInfo.name"
          />
          <AvatarFallback>{{ avatarInfo?.initials }}</AvatarFallback>
        </Avatar>

        <!-- 图片 -->
        <img
          v-else-if="iconConfig.type === 'image' && row[iconConfig.imageField]"
          :src="row[iconConfig.imageField]"
          :alt="mainContent"
          :class="cn('object-cover rounded', iconConfig.sizeClass)"
        />

        <!-- 普通图标 -->
        <Icon
          v-else-if="iconConfig.type === 'icon' && iconConfig.icon"
          :icon="iconConfig.icon"
          :class="iconConfig.sizeClass"
        />
      </div>

      <!-- 内容区域 -->
      <div class="flex-1 min-w-0">
        <!-- 主要内容 -->
        <div
          :class="
            cn('truncate font-medium text-foreground', config.main?.className)
          "
          :style="config.main?.style"
        >
          {{ mainContent }}
        </div>

        <!-- 子内容区域 -->
        <div
          v-if="subContents.items.length > 0"
          :class="[
            'text-sm text-muted-foreground mt-0.5',
            subContents.layout === 'vertical'
              ? 'flex flex-col space-y-0.5'
              : 'flex items-center',
          ]"
        >
          <template
            v-for="(sub, index) in subContents.items"
            :key="`sub-${index}-${sub.field}`"
          >
            <span :class="cn('truncate overflow-hidden', sub.className)">
              {{ sub.content }}
            </span>
            <span
              v-if="
                subContents.layout === 'horizontal' &&
                index < subContents.items.length - 1
              "
              class="text-muted-foreground/60 mx-1"
            >
              {{ subContents.separator }}
            </span>
          </template>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div v-if="actions.shouldRender" :class="actionsClasses">
        <!-- 直接显示的操作按钮 -->
        <template
          v-for="action in actions.direct"
          :key="`action-${action.key || action.icon}`"
        >
          <Tooltip>
            <TooltipTrigger as-child>
              <Button
                :variant="getButtonVariant(action)"
                :size="action.size || 'sm'"
                class="h-6 w-6 p-0 hover:scale-110 transition-transform"
                @click.stop="handleActionClick(action)"
              >
                <Icon :icon="action.icon" class="h-3 w-3" />
                <span class="sr-only">{{ action.tooltip || action.text }}</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent v-if="action.tooltip">
              {{ action.tooltip }}
            </TooltipContent>
          </Tooltip>
        </template>

        <!-- 更多操作下拉菜单 -->
        <DropdownMenu
          v-if="actions.dropdown.length > 0"
          @update:open="handleDropdownToggle"
        >
          <DropdownMenuTrigger as-child>
            <Button
              variant="ghost"
              size="sm"
              class="h-6 w-6 p-0 hover:scale-110 transition-transform"
            >
              <Icon icon="mdi:dots-horizontal" class="h-3 w-3" />
              <span class="sr-only">更多操作</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-40">
            <DropdownMenuItem
              v-for="action in actions.dropdown"
              :key="`dropdown-${action.key || action.icon}`"
              :class="cn('cursor-pointer', getDropdownItemClasses(action))"
              @click.stop="handleActionClick(action)"
            >
              <Icon
                v-if="action.icon"
                :icon="action.icon"
                class="mr-2 h-4 w-4"
              />
              {{ action.text || action.tooltip }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  </TooltipProvider>
</template>

<script setup lang="ts">
defineOptions({
  name: 'CompositeRenderer',
})

import { ref, computed, onMounted, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { useAutoRowHeight } from '@/components/data-grid/utils/rowHeightHelpers'

// 常量定义
const DEFAULT_ICON_SIZE = 32

// 获取 Tailwind CSS 尺寸类名
const getTailwindSizeClass = (size: number): string => {
  const units = Math.floor(size / 4)
  return `w-${units} h-${units}`
}

// 类型定义
interface MainContentConfig {
  field?: string
  formatter?: (value: any, row: any) => string
  className?: string
  style?: Record<string, any>
}

interface SubContentItem {
  field: string
  label?: string
  formatter?: (value: any, row: any) => string
  condition?: (row: any) => boolean
  className?: string
}

interface SubContentConfig {
  items?: SubContentItem[]
  layout?: 'horizontal' | 'vertical'
  separator?: string
}

interface IconConfig {
  type?: 'icon' | 'image' | 'avatar'
  icon?: string
  imageField?: string
  avatarField?: string
  nameField?: string
  size?: number
}

interface ActionButton {
  key?: string
  text?: string
  icon: string
  tooltip?: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  variant?:
    | 'link'
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
  size?: 'icon' | 'default' | 'xs' | 'sm' | 'lg'
  onClick: (row: any) => void
  condition?: (row: any) => boolean
}

interface CompositeConfig {
  main?: MainContentConfig
  subs?: SubContentConfig
  icon?: IconConfig
  actions?: ActionButton[]
  showActionsCount?: number
  enableHover?: boolean
  width?: number
}

interface Props {
  value?: any
  row: any
  config?: CompositeConfig
  field?: string
}

const props = defineProps<Props>()

// 悬停状态
const isHovered = ref(false)
const isDropdownOpen = ref(false)
const containerRef = ref<HTMLElement>()

// 默认配置
const DEFAULT_CONFIG: CompositeConfig = {
  main: { field: 'name' },
  subs: { items: [], layout: 'horizontal', separator: '·' },
  showActionsCount: 1,
  enableHover: true,
}

// 合并配置
const config = computed(() => ({
  ...DEFAULT_CONFIG,
  ...props.config,
  subs: {
    ...DEFAULT_CONFIG.subs,
    ...props.config?.subs,
  },
}))

// 主内容处理逻辑
const mainContent = computed(() => {
  const mainConfig = config.value.main
  if (!mainConfig) return String(props.value || '')

  const field = mainConfig.field || 'name'
  const value = props.row[field] || props.value

  if (mainConfig.formatter) {
    try {
      return mainConfig.formatter(value, props.row)
    } catch (error) {
      console.warn('Formatter error:', error)
      return String(value || '')
    }
  }

  return String(value || '')
})

// 子内容处理逻辑
const subContents = computed(() => {
  const subsConfig = config.value.subs
  if (!subsConfig?.items?.length) {
    return { items: [], layout: 'horizontal', separator: '·' }
  }

  const items = subsConfig.items
    .filter((item) => !item.condition || item.condition(props.row))
    .map((item) => {
      const value = props.row[item.field]
      const content = item.formatter
        ? item.formatter(value, props.row)
        : String(value || '')

      return { ...item, content }
    })
    .filter((item) => item.content)

  return {
    items,
    layout: subsConfig.layout || 'horizontal',
    separator: subsConfig.separator || '·',
  }
})

// 图标配置
const iconConfig = computed(() => {
  const iconConf = config.value.icon
  if (!iconConf) return null

  const sizeClass = getTailwindSizeClass(iconConf.size || DEFAULT_ICON_SIZE)
  return { ...iconConf, sizeClass }
})

// 头像信息
const avatarInfo = computed(() => {
  const iconConf = iconConfig.value
  if (!iconConf || iconConf.type !== 'avatar') return null

  const image = props.row[iconConf.avatarField || 'avatar']
  const name = props.row[iconConf.nameField || 'name'] || mainContent.value
  const initials = name
    .split(' ')
    .map((word: string) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)

  return { image, name, initials }
})

// 操作按钮逻辑
const actions = computed(() => {
  const actionsConfig = config.value.actions || []
  const showCount = config.value.showActionsCount || 1

  const visibleActions = actionsConfig.filter(
    (action) => !action.condition || action.condition(props.row)
  )

  const direct = visibleActions.slice(0, showCount)
  const dropdown = visibleActions.slice(showCount)
  const shouldRender =
    visibleActions.length > 0 &&
    (!config.value.enableHover || isHovered.value || isDropdownOpen.value)

  return { direct, dropdown, shouldRender }
})

// 获取按钮变体
const getButtonVariant = (action: ActionButton) => {
  if (action.variant) return action.variant

  // 根据类型映射变体
  switch (action.type) {
    case 'primary':
      return 'default'
    case 'success':
      return 'default'
    case 'warning':
      return 'outline'
    case 'danger':
      return 'destructive'
    case 'info':
      return 'secondary'
    default:
      return 'outline'
  }
}

// 获取下拉菜单项样式类
const getDropdownItemClasses = (action: ActionButton) => {
  const variant = getButtonVariant(action)

  switch (variant) {
    case 'destructive':
      return 'text-destructive focus:text-destructive focus:bg-destructive/10'
    case 'default':
      return 'text-primary focus:text-primary focus:bg-primary/10'
    case 'secondary':
      return 'text-secondary-foreground focus:text-secondary-foreground focus:bg-secondary/10'
    case 'outline':
      return 'text-muted-foreground focus:text-foreground focus:bg-muted/10'
    case 'ghost':
      return 'text-muted-foreground focus:text-foreground focus:bg-muted/10'
    case 'link':
      return 'text-primary focus:text-primary focus:bg-primary/10'
    default:
      return 'text-muted-foreground focus:text-foreground focus:bg-muted/10'
  }
}

// 样式类
const containerClasses = computed(() => {
  return cn(
    'flex items-center gap-2 p-1 rounded transition-colors duration-200',
    config.value.enableHover && 'hover:bg-muted/50'
  )
})

const actionsClasses = computed(() => {
  return cn(
    'flex items-center gap-1 opacity-0 transition-opacity duration-200',
    (actions.value.shouldRender || !config.value.enableHover) && 'opacity-100'
  )
})

// 事件处理
const handleActionClick = (action: ActionButton) => {
  // 执行操作
  action.onClick(props.row)

  // 隐藏按钮区域
  isHovered.value = false
  isDropdownOpen.value = false
}

const handleMouseLeave = () => {
  setTimeout(() => {
    if (!isDropdownOpen.value) {
      isHovered.value = false
    }
  }, 100)
}

const handleDropdownToggle = (open: boolean) => {
  isDropdownOpen.value = open
  if (open) isHovered.value = true
}

// 使用自动行高调整工具
const { adjustHeight } = useAutoRowHeight(containerRef, config, {
  addMarkerClass: true,
  minHeight: 40,
  padding: 8,
})

// 监听内容变化，自动调整行高
watch(
  [subContents, iconConfig, actions],
  () => {
    adjustHeight()
  },
  { deep: true }
)

// 组件挂载后调整行高
onMounted(() => {
  adjustHeight()
})
</script>
