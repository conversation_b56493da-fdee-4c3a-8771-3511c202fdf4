/**
 * DataGrid 行高自动调整工具函数
 * 
 * 用于在渲染器组件中自动调整表格行高以适应内容
 */

import { nextTick } from 'vue'

/**
 * 自动调整表格行高以适应内容
 * 
 * @param containerElement - 渲染器容器元素
 * @param options - 调整选项
 */
export interface AutoRowHeightOptions {
  /** 最小行高，默认40px */
  minHeight?: number
  /** 是否强制调整，默认false */
  force?: boolean
  /** 额外的内边距，默认8px */
  padding?: number
  /** 是否添加标识类，默认true */
  addMarkerClass?: boolean
}

export function adjustRowHeight(
  containerElement: HTMLElement | null,
  options: AutoRowHeightOptions = {}
): void {
  if (!containerElement) return

  const {
    minHeight = 40,
    force = false,
    padding = 8,
    addMarkerClass = true
  } = options

  nextTick(() => {
    // 查找最近的表格行元素
    let rowElement = containerElement.closest('.vxe-body--row') as HTMLElement
    if (!rowElement) {
      rowElement = containerElement.closest('tr') as HTMLElement
    }

    if (!rowElement) return

    // 添加标识类，启用自动行高样式
    if (addMarkerClass) {
      rowElement.classList.add('has-auto-height-renderer')
    }

    // 获取容器的实际高度
    const containerHeight = containerElement.scrollHeight
    const requiredHeight = Math.max(containerHeight + padding, minHeight)
    const currentHeight = rowElement.offsetHeight

    // 只有在需要更多空间或强制调整时才调整行高
    if (force || requiredHeight > currentHeight) {
      // 设置行高为自动
      rowElement.style.height = 'auto'
      rowElement.style.minHeight = `${requiredHeight}px`

      // 确保所有单元格也使用自动高度
      const cells = rowElement.querySelectorAll('.vxe-body--column')
      cells.forEach((cell: Element) => {
        if (cell instanceof HTMLElement) {
          cell.style.height = 'auto'
          cell.style.minHeight = `${requiredHeight}px`
        }
      })
    }
  })
}

/**
 * 检查是否需要自动调整行高
 * 
 * @param config - 渲染器配置
 * @returns 是否需要调整
 */
export function shouldAdjustRowHeight(config: any): boolean {
  // 检查是否有可能导致多行内容的配置
  const hasVerticalLayout = config?.subs?.layout === 'vertical'
  const hasMultipleSubItems = (config?.subs?.items?.length || 0) > 1
  const hasIcon = !!config?.icon
  const hasActions = (config?.actions?.length || 0) > 0
  const hasLongContent = config?.main?.multiline || config?.allowWrap

  return hasVerticalLayout || 
         (hasMultipleSubItems && hasIcon) || 
         hasActions || 
         hasLongContent
}

/**
 * 为渲染器组件创建自动行高调整的组合式函数
 * 
 * @param containerRef - 容器元素的ref
 * @param configRef - 配置的ref
 * @param options - 调整选项
 */
export function useAutoRowHeight(
  containerRef: { value: HTMLElement | null },
  configRef: { value: any },
  options: AutoRowHeightOptions = {}
) {
  const adjustHeight = () => {
    if (shouldAdjustRowHeight(configRef.value)) {
      adjustRowHeight(containerRef.value, options)
    }
  }

  return {
    adjustHeight,
    shouldAdjust: () => shouldAdjustRowHeight(configRef.value)
  }
}

/**
 * 重置行高为默认值
 * 
 * @param containerElement - 渲染器容器元素
 */
export function resetRowHeight(containerElement: HTMLElement | null): void {
  if (!containerElement) return

  const rowElement = containerElement.closest('.vxe-body--row') as HTMLElement
  if (!rowElement) return

  // 移除标识类
  rowElement.classList.remove('has-auto-height-renderer', 'has-composite-renderer')
  
  // 重置样式
  rowElement.style.height = ''
  rowElement.style.minHeight = ''

  // 重置所有单元格样式
  const cells = rowElement.querySelectorAll('.vxe-body--column')
  cells.forEach((cell: Element) => {
    if (cell instanceof HTMLElement) {
      cell.style.height = ''
      cell.style.minHeight = ''
    }
  })
}
